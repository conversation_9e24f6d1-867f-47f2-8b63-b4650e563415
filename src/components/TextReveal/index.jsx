// components/TextReveal.jsx
'use client';

import { useRef, useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import SplitType from 'split-type'

export default function TextReveal({
  as: Tag = 'div',
  children,
  className,
  stagger = 0.12,
  delayStart = 0,
  ...props
}) {
  const ref = useRef()
  const [lines, setLines] = useState([])

  useEffect(() => {
    const split = new SplitType(ref.current, {
      types: 'lines',
      lineTag: 'div',
    })
    setLines(split.lines.map(el => el.textContent))
    split.revert()
  }, [children])

  const container = {
    hidden: {},
    show: {
      transition: {
        delayChildren: delayStart,
        staggerChildren: stagger,
      }
    }
  }

  // on passe index `i` à show pour customiser le delay
  const item = {
    hidden: {
      y: 40,
      opacity: 0,
      clipPath: 'inset(0 0 100% 0)',  
    },
    show: i => ({
      y: 0,
      opacity: 1,
      clipPath: 'inset(0 0 0% 0)',
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 18,
        mass: 0.6,
        delay: i * (stagger * 0.9),
      }
    })
  }

  // phase de mesure (texte caché)
  if (!lines.length) {
    return (
      <Tag
        ref={ref}
        className={className}
        style={{ visibility: 'hidden' }}
        {...props}
      >
        {children}
      </Tag>
    )
  }

  return (
    <Tag
      className={className}
      {...props}
    >
      <motion.div
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, margin: '-50px' }}
        style={{ overflow: 'hidden' }}
      >
        {lines.map((line, i) => (
          <motion.div
            key={i}
            custom={i}
            variants={item}
            style={{ overflow: 'hidden' }}
          >
            <span style={{ display: 'inline-block', whiteSpace: 'pre-wrap' }}>
              {line}
            </span>
          </motion.div>
        ))}
      </motion.div>
    </Tag>
  )
}
