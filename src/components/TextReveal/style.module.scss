// Styles pour forcer l'héritage des propriétés typographiques
.textRevealLine {
  display: inline-block;
  white-space: pre-wrap;

  // Force l'héritage de toutes les propriétés typographiques du parent
  font-size: inherit !important;
  font-weight: inherit !important;
  font-family: inherit !important;
  letter-spacing: inherit !important;
  word-spacing: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  text-transform: inherit !important;
  font-style: inherit !important;
}

// Styles spécifiques pour les h2 dans TextReveal
:global(h2) .textRevealLine {
  font-size: calc(1.8rem + 1.5vw) !important;
  font-weight: 500 !important;
  letter-spacing: calc(-1px - 0.2vw) !important;
  word-spacing: calc(4px + 0.4vw) !important;
}
